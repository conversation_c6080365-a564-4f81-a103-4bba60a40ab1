[package]
name = "axum-test"
version = "0.1.0"
edition = "2024"

[dependencies]
axum = { version = "0.8.4", features = ["ws"] }
axum-extra = { version = "0.10.1", features = ["typed-header"] }
futures-util = { version = "0.3", default-features = false, features = ["sink", "std"] }
headers = "0.4"
tokio = { version = "1.0", features = ["full"] }
tokio-tungstenite = "0.27.0"
tower-http = { version = "0.6.1", features = ["fs", "trace"] }
tracing = "0.1"
tracing-subscriber = { version = "0.3", features = ["env-filter"] }
